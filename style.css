* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #0052CC 0%, #0747A6 100%);
    min-height: 100vh;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

header {
    background: linear-gradient(135deg, #0052CC 0%, #0747A6 100%);
    color: white;
    text-align: center;
    padding: 30px 20px;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    font-weight: 700;
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.bingo-board {
    padding: 30px;
}

.column-headers {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.header {
    padding: 15px 10px;
    text-align: center;
    font-weight: bold;
    color: white;
    border-radius: 8px;
    font-size: 0.9rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.bingo-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin-bottom: 30px;
}

.bingo-cell {
    aspect-ratio: 1;
    border: 3px solid transparent;
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 120px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.bingo-cell:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.task-text {
    font-size: 0.85rem;
    line-height: 1.3;
    font-weight: 500;
    color: white;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    z-index: 1;
}

.tick-mark {
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 1.5rem;
    color: #fff;
    opacity: 0;
    transition: all 0.3s ease;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.bingo-cell.checked .tick-mark {
    opacity: 1;
    background: rgba(255, 255, 255, 0.9);
    color: #2e7d32;
}

.bingo-cell.checked {
    border-color: #fff;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* Color schemes for each value - Using Atlassian official colors */
.open-company {
    background: linear-gradient(135deg, #FF5630, #DE350B);
}

.build-heart {
    background: linear-gradient(135deg, #36B37E, #00875A);
}

.dont-customer {
    background: linear-gradient(135deg, #0052CC, #0747A6);
}

.play-team {
    background: linear-gradient(135deg, #6554C0, #5243AA);
}

.be-change {
    background: linear-gradient(135deg, #FFAB00, #FF991F);
    color: #172B4D !important;
}

.be-change .task-text {
    color: #172B4D !important;
    text-shadow: none;
}



.legend {
    background: #f8f9fa;
    padding: 30px;
    border-top: 1px solid #e9ecef;
}

.legend h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 1.5rem;
}

.legend-items {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.legend-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
}

.status {
    padding: 30px;
    text-align: center;
    background: #f8f9fa;
}

.win-message {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 20px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.win-message.show {
    color: #2e7d32;
    animation: celebration 0.6s ease-in-out;
}

@keyframes celebration {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); opacity: 1; }
}

.reset-button {
    background: linear-gradient(135deg, #0052CC, #0065FF);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.reset-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(0, 82, 204, 0.3);
}

/* Winning line highlight */
.winning-line {
    background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
    animation: pulse 1s infinite alternate;
}

@keyframes pulse {
    0% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.7); }
    100% { box-shadow: 0 0 30px rgba(255, 215, 0, 1); }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .bingo-board {
        padding: 20px;
    }
    
    .bingo-cell {
        min-height: 100px;
        padding: 10px;
    }
    
    .task-text {
        font-size: 0.75rem;
    }
    
    .header {
        font-size: 0.8rem;
        padding: 10px 5px;
    }
    
    .legend-items {
        flex-direction: column;
        align-items: center;
    }
}

@media (max-width: 480px) {
    .bingo-cell {
        min-height: 80px;
        padding: 8px;
    }
    
    .task-text {
        font-size: 0.7rem;
    }
    
    .tick-mark {
        width: 25px;
        height: 25px;
        font-size: 1.2rem;
    }
}
